
> vite_react_shadcn_ts@0.0.0 dev
> vite

Port 8080 is in use, trying another one...

  VITE v6.3.5  ready in 176 ms

  ➜  Local:   http://localhost:8081/
 11:12:12 AM [vite] (client) hmr update /src/components/ui/label.tsx, /src/index.css
11:28:44 AM [vite] (client) hmr update /src/components/EmailProcessing.tsx, /src/index.css
11:29:01 AM [vite] (client) hmr update /src/components/EmailProcessing.tsx, /src/index.css
