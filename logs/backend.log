INFO:     Will watch for changes in these directories: ['/Users/<USER>/Desktop/OCR/KharchaNepal/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [27387] using WatchFiles
INFO:     Started server process [27391]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
2025-06-24 11:09:48,988 INFO sqlalchemy.engine.Engine select pg_catalog.version()
INFO:sqlalchemy.engine.Engine:select pg_catalog.version()
2025-06-24 11:09:48,989 INFO sqlalchemy.engine.Engine [raw sql] ()
INFO:sqlalchemy.engine.Engine:[raw sql] ()
2025-06-24 11:09:48,998 INFO sqlalchemy.engine.Engine select current_schema()
INFO:sqlalchemy.engine.Engine:select current_schema()
2025-06-24 11:09:48,999 INFO sqlalchemy.engine.Engine [raw sql] ()
INFO:sqlalchemy.engine.Engine:[raw sql] ()
2025-06-24 11:09:49,005 INFO sqlalchemy.engine.Engine show standard_conforming_strings
INFO:sqlalchemy.engine.Engine:show standard_conforming_strings
2025-06-24 11:09:49,005 INFO sqlalchemy.engine.Engine [raw sql] ()
INFO:sqlalchemy.engine.Engine:[raw sql] ()
2025-06-24 11:09:49,007 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:09:49,034 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:09:49,034 INFO sqlalchemy.engine.Engine [generated in 0.00036s] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[generated in 0.00036s] ('<EMAIL>',)
WARNING:passlib.handlers.bcrypt:(trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-06-24 11:09:49,378 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:50982 - "POST /api/auth/login HTTP/1.1" 200 OK
INFO:     127.0.0.1:50982 - "OPTIONS /api/auth/users/me HTTP/1.1" 200 OK
--- get_current_user: Received token: eyJhbGciOi...XbJ25f_qTA
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750744489}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:09:49,413 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:09:49,414 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:09:49,414 INFO sqlalchemy.engine.Engine [cached since 0.3797s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 0.3797s ago] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:09:49,417 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:50982 - "GET /api/auth/users/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:50984 - "OPTIONS /api/dashboard/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:50985 - "OPTIONS /api/dashboard/summary HTTP/1.1" 200 OK
INFO:     127.0.0.1:50986 - "OPTIONS /api/expenses?limit=5&skip=0 HTTP/1.1" 200 OK
INFO:     127.0.0.1:50987 - "OPTIONS /api/dashboard/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:50988 - "OPTIONS /api/dashboard/summary HTTP/1.1" 200 OK
INFO:     127.0.0.1:50982 - "OPTIONS /api/expenses?limit=5&skip=0 HTTP/1.1" 200 OK
--- get_current_user: Received token: eyJhbGciOi...XbJ25f_qTA
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750744489}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:09:49,423 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:09:49,424 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:09:49,424 INFO sqlalchemy.engine.Engine [cached since 0.3903s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 0.3903s ago] ('<EMAIL>',)
--- get_current_user: Received token: eyJhbGciOi...XbJ25f_qTA
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750744489}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
--- get_current_user: Received token: eyJhbGciOi...XbJ25f_qTA
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750744489}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
--- get_current_user: Received token: eyJhbGciOi...XbJ25f_qTA
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750744489}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
--- get_current_user: Received token: eyJhbGciOi...XbJ25f_qTA
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750744489}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
--- get_current_user: Received token: eyJhbGciOi...XbJ25f_qTA
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750744489}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:09:49,436 INFO sqlalchemy.engine.Engine SELECT sum(expenses.amount) AS sum_1 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT sum(expenses.amount) AS sum_1 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER
2025-06-24 11:09:49,436 INFO sqlalchemy.engine.Engine [generated in 0.00015s] (1, 2025, 6)
INFO:sqlalchemy.engine.Engine:[generated in 0.00015s] (1, 2025, 6)
2025-06-24 11:09:49,470 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:09:49,470 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:09:49,471 INFO sqlalchemy.engine.Engine [cached since 0.4366s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 0.4366s ago] ('<EMAIL>',)
2025-06-24 11:09:49,476 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER ORDER BY expenses.amount DESC 
 LIMIT $4::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER ORDER BY expenses.amount DESC 
 LIMIT $4::INTEGER
2025-06-24 11:09:49,476 INFO sqlalchemy.engine.Engine [generated in 0.00018s] (1, 2025, 6, 1)
INFO:sqlalchemy.engine.Engine:[generated in 0.00018s] (1, 2025, 6, 1)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:09:49,478 INFO sqlalchemy.engine.Engine SELECT expenses.category, sum(expenses.amount) AS total_amount 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND expenses.date >= $2::DATE GROUP BY expenses.category ORDER BY total_amount DESC
INFO:sqlalchemy.engine.Engine:SELECT expenses.category, sum(expenses.amount) AS total_amount 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND expenses.date >= $2::DATE GROUP BY expenses.category ORDER BY total_amount DESC
2025-06-24 11:09:49,478 INFO sqlalchemy.engine.Engine [generated in 0.00027s] (1, datetime.date(2025, 5, 25))
INFO:sqlalchemy.engine.Engine:[generated in 0.00027s] (1, datetime.date(2025, 5, 25))
2025-06-24 11:09:49,480 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:09:49,480 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:09:49,480 INFO sqlalchemy.engine.Engine [cached since 0.4464s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 0.4464s ago] ('<EMAIL>',)
2025-06-24 11:09:49,481 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:09:49,481 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:09:49,481 INFO sqlalchemy.engine.Engine [cached since 0.4474s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 0.4474s ago] ('<EMAIL>',)
2025-06-24 11:09:49,481 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:09:49,482 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:09:49,482 INFO sqlalchemy.engine.Engine [cached since 0.4476s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 0.4476s ago] ('<EMAIL>',)
2025-06-24 11:09:49,482 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:09:49,482 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:09:49,482 INFO sqlalchemy.engine.Engine [cached since 0.4478s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 0.4478s ago] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:09:49,484 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
2025-06-24 11:09:49,484 INFO sqlalchemy.engine.Engine [generated in 0.00012s] (1, 5, 0)
INFO:sqlalchemy.engine.Engine:[generated in 0.00012s] (1, 5, 0)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:09:49,488 INFO sqlalchemy.engine.Engine SELECT expenses.category, sum(expenses.amount) AS total_amount 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND expenses.date >= $2::DATE GROUP BY expenses.category ORDER BY total_amount DESC
INFO:sqlalchemy.engine.Engine:SELECT expenses.category, sum(expenses.amount) AS total_amount 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND expenses.date >= $2::DATE GROUP BY expenses.category ORDER BY total_amount DESC
2025-06-24 11:09:49,488 INFO sqlalchemy.engine.Engine [cached since 0.009522s ago] (1, datetime.date(2025, 5, 25))
INFO:sqlalchemy.engine.Engine:[cached since 0.009522s ago] (1, datetime.date(2025, 5, 25))
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:09:49,488 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
2025-06-24 11:09:49,488 INFO sqlalchemy.engine.Engine [cached since 0.004114s ago] (1, 5, 0)
INFO:sqlalchemy.engine.Engine:[cached since 0.004114s ago] (1, 5, 0)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:09:49,489 INFO sqlalchemy.engine.Engine SELECT sum(expenses.amount) AS sum_1 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT sum(expenses.amount) AS sum_1 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER
2025-06-24 11:09:49,489 INFO sqlalchemy.engine.Engine [cached since 0.05279s ago] (1, 2025, 6)
INFO:sqlalchemy.engine.Engine:[cached since 0.05279s ago] (1, 2025, 6)
2025-06-24 11:09:49,490 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER ORDER BY expenses.amount DESC 
 LIMIT $4::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER ORDER BY expenses.amount DESC 
 LIMIT $4::INTEGER
2025-06-24 11:09:49,490 INFO sqlalchemy.engine.Engine [cached since 0.01479s ago] (1, 2025, 6, 1)
INFO:sqlalchemy.engine.Engine:[cached since 0.01479s ago] (1, 2025, 6, 1)
2025-06-24 11:09:49,516 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-06-24 11:09:49,516 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:50987 - "GET /api/dashboard/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:50985 - "GET /api/dashboard/summary HTTP/1.1" 200 OK
2025-06-24 11:09:49,518 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-06-24 11:09:49,518 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:50984 - "GET /api/dashboard/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:50988 - "GET /api/dashboard/summary HTTP/1.1" 200 OK
2025-06-24 11:09:49,521 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-06-24 11:09:49,521 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:50986 - "GET /api/expenses?limit=5&skip=0 HTTP/1.1" 200 OK
INFO:     127.0.0.1:50982 - "GET /api/expenses?limit=5&skip=0 HTTP/1.1" 200 OK
INFO:     127.0.0.1:50987 - "OPTIONS /api/user/profile HTTP/1.1" 200 OK
INFO:     127.0.0.1:50985 - "OPTIONS /api/user/profile HTTP/1.1" 200 OK
--- get_current_user: Received token: eyJhbGciOi...XbJ25f_qTA
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750744489}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:09:51,536 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:09:51,537 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:09:51,537 INFO sqlalchemy.engine.Engine [cached since 2.503s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 2.503s ago] ('<EMAIL>',)
--- get_current_user: Received token: eyJhbGciOi...XbJ25f_qTA
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750744489}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:09:51,539 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:09:51,539 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:09:51,539 INFO sqlalchemy.engine.Engine [cached since 2.505s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 2.505s ago] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:09:51,540 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:09:51,541 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:50988 - "GET /api/user/profile HTTP/1.1" 200 OK
INFO:     127.0.0.1:50984 - "GET /api/user/profile HTTP/1.1" 200 OK
--- get_current_user: Received token: eyJhbGciOi...XbJ25f_qTA
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750744489}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:10:11,570 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:10:11,570 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:10:11,571 INFO sqlalchemy.engine.Engine [cached since 22.54s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 22.54s ago] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:10:11,574 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:50999 - "GET /api/email/oauth/authorize HTTP/1.1" 200 OK
ERROR:src.email_processing.gmail_service:Error exchanging code for tokens: Scope has changed from "https://www.googleapis.com/auth/gmail.readonly https://www.googleapis.com/auth/userinfo.email" to "https://www.googleapis.com/auth/gmail.readonly openid https://www.googleapis.com/auth/userinfo.email".
ERROR:src.email_processing.router:Error handling OAuth callback: Scope has changed from "https://www.googleapis.com/auth/gmail.readonly https://www.googleapis.com/auth/userinfo.email" to "https://www.googleapis.com/auth/gmail.readonly openid https://www.googleapis.com/auth/userinfo.email".
INFO:     127.0.0.1:51017 - "GET /api/email/oauth/callback?state=1&code=4%2F0AUJR-x5lg9zPmEsYzNvF7RbTS91_7p1PuDqYd4UebgXT2oqKxw-na5tgl5h-DLzQSR7iEA&scope=email+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fgmail.readonly+openid&authuser=0&prompt=none HTTP/1.1" 302 Found
--- get_current_user: Received token: eyJhbGciOi...XbJ25f_qTA
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750744489}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:10:20,978 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:10:20,979 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:10:20,979 INFO sqlalchemy.engine.Engine [cached since 31.95s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 31.95s ago] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:10:20,981 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:51017 - "GET /api/auth/users/me HTTP/1.1" 200 OK
--- get_current_user: Received token: eyJhbGciOi...XbJ25f_qTA
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750744489}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:10:20,997 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:10:20,997 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:10:20,997 INFO sqlalchemy.engine.Engine [cached since 31.96s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 31.96s ago] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:10:20,999 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:51017 - "GET /api/user/profile HTTP/1.1" 200 OK
--- get_current_user: Received token: eyJhbGciOi...XbJ25f_qTA
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750744489}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:11:09,091 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:11:09,091 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:11:09,091 INFO sqlalchemy.engine.Engine [cached since 80.06s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 80.06s ago] ('<EMAIL>',)
--- get_current_user: Received token: eyJhbGciOi...XbJ25f_qTA
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750744489}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:11:09,093 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:11:09,093 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:11:09,094 INFO sqlalchemy.engine.Engine [cached since 80.06s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 80.06s ago] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:11:09,104 INFO sqlalchemy.engine.Engine SELECT transaction_approvals.id, transaction_approvals.user_id, transaction_approvals.email_message_id, transaction_approvals.extracted_data, transaction_approvals.approval_status, transaction_approvals.confidence_score, transaction_approvals.created_at, transaction_approvals.updated_at 
FROM transaction_approvals 
WHERE transaction_approvals.user_id = $1::INTEGER AND transaction_approvals.approval_status = $2::approvalstatusenum ORDER BY transaction_approvals.created_at DESC
INFO:sqlalchemy.engine.Engine:SELECT transaction_approvals.id, transaction_approvals.user_id, transaction_approvals.email_message_id, transaction_approvals.extracted_data, transaction_approvals.approval_status, transaction_approvals.confidence_score, transaction_approvals.created_at, transaction_approvals.updated_at 
FROM transaction_approvals 
WHERE transaction_approvals.user_id = $1::INTEGER AND transaction_approvals.approval_status = $2::approvalstatusenum ORDER BY transaction_approvals.created_at DESC
2025-06-24 11:11:09,105 INFO sqlalchemy.engine.Engine [generated in 0.00093s] (1, 'PENDING')
INFO:sqlalchemy.engine.Engine:[generated in 0.00093s] (1, 'PENDING')
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:11:09,106 INFO sqlalchemy.engine.Engine SELECT transaction_approvals.id, transaction_approvals.user_id, transaction_approvals.email_message_id, transaction_approvals.extracted_data, transaction_approvals.approval_status, transaction_approvals.confidence_score, transaction_approvals.created_at, transaction_approvals.updated_at 
FROM transaction_approvals 
WHERE transaction_approvals.user_id = $1::INTEGER AND transaction_approvals.approval_status = $2::approvalstatusenum ORDER BY transaction_approvals.created_at DESC
INFO:sqlalchemy.engine.Engine:SELECT transaction_approvals.id, transaction_approvals.user_id, transaction_approvals.email_message_id, transaction_approvals.extracted_data, transaction_approvals.approval_status, transaction_approvals.confidence_score, transaction_approvals.created_at, transaction_approvals.updated_at 
FROM transaction_approvals 
WHERE transaction_approvals.user_id = $1::INTEGER AND transaction_approvals.approval_status = $2::approvalstatusenum ORDER BY transaction_approvals.created_at DESC
2025-06-24 11:11:09,106 INFO sqlalchemy.engine.Engine [cached since 0.002711s ago] (1, 'PENDING')
INFO:sqlalchemy.engine.Engine:[cached since 0.002711s ago] (1, 'PENDING')
2025-06-24 11:11:09,130 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-06-24 11:11:09,130 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:51036 - "GET /api/email/approvals?status=PENDING HTTP/1.1" 200 OK
INFO:     127.0.0.1:51034 - "GET /api/email/approvals?status=PENDING HTTP/1.1" 200 OK
INFO:     127.0.0.1:51933 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:51933 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51998 - "OPTIONS /api/auth/users/me HTTP/1.1" 200 OK
--- get_current_user: Received token: eyJhbGciOi...XbJ25f_qTA
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750744489}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:18,503 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:18,504 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:18,505 INFO sqlalchemy.engine.Engine [cached since 809.5s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 809.5s ago] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:18,589 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:51998 - "GET /api/auth/users/me HTTP/1.1" 200 OK
--- get_current_user: Received token: eyJhbGciOi...XbJ25f_qTA
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750744489}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:19,122 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:19,123 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:19,123 INFO sqlalchemy.engine.Engine [cached since 810.1s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 810.1s ago] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:19,125 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:51998 - "GET /api/auth/users/me HTTP/1.1" 200 OK
2025-06-24 11:23:21,161 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:21,162 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:21,163 INFO sqlalchemy.engine.Engine [cached since 812.1s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 812.1s ago] ('<EMAIL>',)
2025-06-24 11:23:21,422 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:51998 - "POST /api/auth/login HTTP/1.1" 200 OK
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:21,428 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:21,428 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:21,428 INFO sqlalchemy.engine.Engine [cached since 812.4s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 812.4s ago] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:21,437 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:51998 - "GET /api/auth/users/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:51998 - "OPTIONS /api/dashboard/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:52002 - "OPTIONS /api/dashboard/summary HTTP/1.1" 200 OK
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:21,643 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:21,643 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:21,643 INFO sqlalchemy.engine.Engine [cached since 812.6s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 812.6s ago] ('<EMAIL>',)
INFO:     127.0.0.1:52003 - "OPTIONS /api/expenses?limit=5&skip=0 HTTP/1.1" 200 OK
INFO:     127.0.0.1:52004 - "OPTIONS /api/dashboard/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:52005 - "OPTIONS /api/dashboard/summary HTTP/1.1" 200 OK
INFO:     127.0.0.1:52006 - "OPTIONS /api/expenses?limit=5&skip=0 HTTP/1.1" 200 OK
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:21,646 INFO sqlalchemy.engine.Engine SELECT sum(expenses.amount) AS sum_1 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT sum(expenses.amount) AS sum_1 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER
2025-06-24 11:23:21,647 INFO sqlalchemy.engine.Engine [cached since 812.2s ago] (1, 2025, 6)
INFO:sqlalchemy.engine.Engine:[cached since 812.2s ago] (1, 2025, 6)
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:21,650 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:21,650 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:21,650 INFO sqlalchemy.engine.Engine [cached since 812.6s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 812.6s ago] ('<EMAIL>',)
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:21,653 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:21,654 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:21,654 INFO sqlalchemy.engine.Engine [cached since 812.6s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 812.6s ago] ('<EMAIL>',)
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:21,655 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:21,656 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:21,656 INFO sqlalchemy.engine.Engine [cached since 812.6s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 812.6s ago] ('<EMAIL>',)
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:21,656 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:21,656 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:21,656 INFO sqlalchemy.engine.Engine [cached since 812.6s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 812.6s ago] ('<EMAIL>',)
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:21,662 INFO sqlalchemy.engine.Engine SELECT sum(expenses.amount) AS sum_1 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT sum(expenses.amount) AS sum_1 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER
2025-06-24 11:23:21,662 INFO sqlalchemy.engine.Engine [cached since 812.2s ago] (1, 2025, 6)
INFO:sqlalchemy.engine.Engine:[cached since 812.2s ago] (1, 2025, 6)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:21,663 INFO sqlalchemy.engine.Engine SELECT expenses.category, sum(expenses.amount) AS total_amount 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND expenses.date >= $2::DATE GROUP BY expenses.category ORDER BY total_amount DESC
INFO:sqlalchemy.engine.Engine:SELECT expenses.category, sum(expenses.amount) AS total_amount 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND expenses.date >= $2::DATE GROUP BY expenses.category ORDER BY total_amount DESC
2025-06-24 11:23:21,663 INFO sqlalchemy.engine.Engine [cached since 812.2s ago] (1, datetime.date(2025, 5, 25))
INFO:sqlalchemy.engine.Engine:[cached since 812.2s ago] (1, datetime.date(2025, 5, 25))
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:21,664 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
2025-06-24 11:23:21,664 INFO sqlalchemy.engine.Engine [cached since 812.2s ago] (1, 5, 0)
INFO:sqlalchemy.engine.Engine:[cached since 812.2s ago] (1, 5, 0)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:21,664 INFO sqlalchemy.engine.Engine SELECT expenses.category, sum(expenses.amount) AS total_amount 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND expenses.date >= $2::DATE GROUP BY expenses.category ORDER BY total_amount DESC
INFO:sqlalchemy.engine.Engine:SELECT expenses.category, sum(expenses.amount) AS total_amount 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND expenses.date >= $2::DATE GROUP BY expenses.category ORDER BY total_amount DESC
2025-06-24 11:23:21,664 INFO sqlalchemy.engine.Engine [cached since 812.2s ago] (1, datetime.date(2025, 5, 25))
INFO:sqlalchemy.engine.Engine:[cached since 812.2s ago] (1, datetime.date(2025, 5, 25))
2025-06-24 11:23:21,669 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER ORDER BY expenses.amount DESC 
 LIMIT $4::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER ORDER BY expenses.amount DESC 
 LIMIT $4::INTEGER
2025-06-24 11:23:21,669 INFO sqlalchemy.engine.Engine [cached since 812.2s ago] (1, 2025, 6, 1)
INFO:sqlalchemy.engine.Engine:[cached since 812.2s ago] (1, 2025, 6, 1)
2025-06-24 11:23:21,672 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52004 - "GET /api/dashboard/stats HTTP/1.1" 200 OK
2025-06-24 11:23:21,674 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER ORDER BY expenses.amount DESC 
 LIMIT $4::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER ORDER BY expenses.amount DESC 
 LIMIT $4::INTEGER
2025-06-24 11:23:21,674 INFO sqlalchemy.engine.Engine [cached since 812.2s ago] (1, 2025, 6, 1)
INFO:sqlalchemy.engine.Engine:[cached since 812.2s ago] (1, 2025, 6, 1)
2025-06-24 11:23:21,674 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-06-24 11:23:21,674 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-06-24 11:23:21,675 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52005 - "GET /api/dashboard/summary HTTP/1.1" 200 OK
INFO:     127.0.0.1:52002 - "GET /api/dashboard/summary HTTP/1.1" 200 OK
INFO:     127.0.0.1:52003 - "GET /api/expenses?limit=5&skip=0 HTTP/1.1" 200 OK
2025-06-24 11:23:21,677 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:51998 - "GET /api/dashboard/stats HTTP/1.1" 200 OK
2025-06-24 11:23:21,700 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:21,700 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:21,700 INFO sqlalchemy.engine.Engine [cached since 812.7s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 812.7s ago] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:21,708 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
2025-06-24 11:23:21,708 INFO sqlalchemy.engine.Engine [cached since 812.2s ago] (1, 5, 0)
INFO:sqlalchemy.engine.Engine:[cached since 812.2s ago] (1, 5, 0)
2025-06-24 11:23:21,735 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52006 - "GET /api/expenses?limit=5&skip=0 HTTP/1.1" 200 OK
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:30,863 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:30,865 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:30,866 INFO sqlalchemy.engine.Engine [cached since 821.8s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 821.8s ago] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:30,870 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
2025-06-24 11:23:30,871 INFO sqlalchemy.engine.Engine [cached since 821.4s ago] (1, 100, 0)
INFO:sqlalchemy.engine.Engine:[cached since 821.4s ago] (1, 100, 0)
2025-06-24 11:23:30,875 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52017 - "GET /api/expenses HTTP/1.1" 200 OK
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:35,498 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:35,498 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:35,498 INFO sqlalchemy.engine.Engine [cached since 826.5s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 826.5s ago] ('<EMAIL>',)
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:35,501 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:35,501 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:35,501 INFO sqlalchemy.engine.Engine [cached since 826.5s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 826.5s ago] ('<EMAIL>',)
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:35,504 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:35,504 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:35,504 INFO sqlalchemy.engine.Engine [cached since 826.5s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 826.5s ago] ('<EMAIL>',)
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:35,506 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:35,506 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:35,507 INFO sqlalchemy.engine.Engine [cached since 826.5s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 826.5s ago] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:35,510 INFO sqlalchemy.engine.Engine SELECT sum(expenses.amount) AS sum_1 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT sum(expenses.amount) AS sum_1 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER
2025-06-24 11:23:35,510 INFO sqlalchemy.engine.Engine [cached since 826.1s ago] (1, 2025, 6)
INFO:sqlalchemy.engine.Engine:[cached since 826.1s ago] (1, 2025, 6)
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:35,513 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:35,514 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:35,514 INFO sqlalchemy.engine.Engine [cached since 826.5s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 826.5s ago] ('<EMAIL>',)
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:35,522 INFO sqlalchemy.engine.Engine SELECT expenses.category, sum(expenses.amount) AS total_amount 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND expenses.date >= $2::DATE GROUP BY expenses.category ORDER BY total_amount DESC
INFO:sqlalchemy.engine.Engine:SELECT expenses.category, sum(expenses.amount) AS total_amount 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND expenses.date >= $2::DATE GROUP BY expenses.category ORDER BY total_amount DESC
2025-06-24 11:23:35,522 INFO sqlalchemy.engine.Engine [cached since 826s ago] (1, datetime.date(2025, 5, 25))
INFO:sqlalchemy.engine.Engine:[cached since 826s ago] (1, datetime.date(2025, 5, 25))
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:35,523 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
2025-06-24 11:23:35,523 INFO sqlalchemy.engine.Engine [cached since 826s ago] (1, 5, 0)
INFO:sqlalchemy.engine.Engine:[cached since 826s ago] (1, 5, 0)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:35,524 INFO sqlalchemy.engine.Engine SELECT expenses.category, sum(expenses.amount) AS total_amount 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND expenses.date >= $2::DATE GROUP BY expenses.category ORDER BY total_amount DESC
INFO:sqlalchemy.engine.Engine:SELECT expenses.category, sum(expenses.amount) AS total_amount 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND expenses.date >= $2::DATE GROUP BY expenses.category ORDER BY total_amount DESC
2025-06-24 11:23:35,525 INFO sqlalchemy.engine.Engine [cached since 826s ago] (1, datetime.date(2025, 5, 25))
INFO:sqlalchemy.engine.Engine:[cached since 826s ago] (1, datetime.date(2025, 5, 25))
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:35,525 INFO sqlalchemy.engine.Engine SELECT sum(expenses.amount) AS sum_1 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT sum(expenses.amount) AS sum_1 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER
2025-06-24 11:23:35,525 INFO sqlalchemy.engine.Engine [cached since 826.1s ago] (1, 2025, 6)
INFO:sqlalchemy.engine.Engine:[cached since 826.1s ago] (1, 2025, 6)
2025-06-24 11:23:35,526 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-06-24 11:23:35,526 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER ORDER BY expenses.amount DESC 
 LIMIT $4::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER ORDER BY expenses.amount DESC 
 LIMIT $4::INTEGER
2025-06-24 11:23:35,526 INFO sqlalchemy.engine.Engine [cached since 826s ago] (1, 2025, 6, 1)
INFO:sqlalchemy.engine.Engine:[cached since 826s ago] (1, 2025, 6, 1)
2025-06-24 11:23:35,527 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-06-24 11:23:35,527 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER ORDER BY expenses.amount DESC 
 LIMIT $4::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER ORDER BY expenses.amount DESC 
 LIMIT $4::INTEGER
2025-06-24 11:23:35,527 INFO sqlalchemy.engine.Engine [cached since 826s ago] (1, 2025, 6, 1)
INFO:sqlalchemy.engine.Engine:[cached since 826s ago] (1, 2025, 6, 1)
INFO:     127.0.0.1:52023 - "GET /api/dashboard/summary HTTP/1.1" 200 OK
INFO:     127.0.0.1:52024 - "GET /api/expenses?limit=5&skip=0 HTTP/1.1" 200 OK
2025-06-24 11:23:35,528 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-06-24 11:23:35,529 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-06-24 11:23:35,529 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52025 - "GET /api/dashboard/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:52026 - "GET /api/dashboard/summary HTTP/1.1" 200 OK
INFO:     127.0.0.1:52022 - "GET /api/dashboard/stats HTTP/1.1" 200 OK
2025-06-24 11:23:35,538 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:35,539 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:35,539 INFO sqlalchemy.engine.Engine [cached since 826.5s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 826.5s ago] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:35,542 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
2025-06-24 11:23:35,542 INFO sqlalchemy.engine.Engine [cached since 826.1s ago] (1, 5, 0)
INFO:sqlalchemy.engine.Engine:[cached since 826.1s ago] (1, 5, 0)
2025-06-24 11:23:35,567 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52027 - "GET /api/expenses?limit=5&skip=0 HTTP/1.1" 200 OK
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:41,685 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:41,685 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:41,685 INFO sqlalchemy.engine.Engine [cached since 832.6s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 832.6s ago] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:41,687 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
2025-06-24 11:23:41,687 INFO sqlalchemy.engine.Engine [cached since 832.2s ago] (1, 100, 0)
INFO:sqlalchemy.engine.Engine:[cached since 832.2s ago] (1, 100, 0)
2025-06-24 11:23:41,691 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52032 - "GET /api/expenses HTTP/1.1" 200 OK
INFO:     127.0.0.1:52036 - "OPTIONS /api/chatbot/query HTTP/1.1" 200 OK
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:46,505 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:46,505 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:46,505 INFO sqlalchemy.engine.Engine [cached since 837.5s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 837.5s ago] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:46,584 INFO sqlalchemy.engine.Engine SELECT sum(expenses.amount) AS sum_1 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND expenses.date >= $2::DATE AND expenses.date <= $3::DATE AND expenses.category = $4::categoryenum
INFO:sqlalchemy.engine.Engine:SELECT sum(expenses.amount) AS sum_1 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND expenses.date >= $2::DATE AND expenses.date <= $3::DATE AND expenses.category = $4::categoryenum
2025-06-24 11:23:46,585 INFO sqlalchemy.engine.Engine [generated in 0.00018s] (1, datetime.date(2025, 5, 1), datetime.date(2025, 5, 31), 'FOOD')
INFO:sqlalchemy.engine.Engine:[generated in 0.00018s] (1, datetime.date(2025, 5, 1), datetime.date(2025, 5, 31), 'FOOD')
2025-06-24 11:23:46,587 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52036 - "POST /api/chatbot/query HTTP/1.1" 200 OK
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:55,886 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:55,887 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:55,887 INFO sqlalchemy.engine.Engine [cached since 846.8s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 846.8s ago] ('<EMAIL>',)
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:55,890 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:55,891 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:55,891 INFO sqlalchemy.engine.Engine [cached since 846.8s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 846.8s ago] ('<EMAIL>',)
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:55,893 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:55,893 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:55,893 INFO sqlalchemy.engine.Engine [cached since 846.9s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 846.9s ago] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:55,895 INFO sqlalchemy.engine.Engine SELECT sum(expenses.amount) AS sum_1 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT sum(expenses.amount) AS sum_1 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER
2025-06-24 11:23:55,895 INFO sqlalchemy.engine.Engine [cached since 846.5s ago] (1, 2025, 6)
INFO:sqlalchemy.engine.Engine:[cached since 846.5s ago] (1, 2025, 6)
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:55,897 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:55,898 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:55,898 INFO sqlalchemy.engine.Engine [cached since 846.9s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 846.9s ago] ('<EMAIL>',)
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:55,901 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:55,901 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:55,901 INFO sqlalchemy.engine.Engine [cached since 846.9s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 846.9s ago] ('<EMAIL>',)
2025-06-24 11:23:55,902 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER ORDER BY expenses.amount DESC 
 LIMIT $4::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER ORDER BY expenses.amount DESC 
 LIMIT $4::INTEGER
2025-06-24 11:23:55,903 INFO sqlalchemy.engine.Engine [cached since 846.4s ago] (1, 2025, 6, 1)
INFO:sqlalchemy.engine.Engine:[cached since 846.4s ago] (1, 2025, 6, 1)
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:55,906 INFO sqlalchemy.engine.Engine SELECT expenses.category, sum(expenses.amount) AS total_amount 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND expenses.date >= $2::DATE GROUP BY expenses.category ORDER BY total_amount DESC
INFO:sqlalchemy.engine.Engine:SELECT expenses.category, sum(expenses.amount) AS total_amount 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND expenses.date >= $2::DATE GROUP BY expenses.category ORDER BY total_amount DESC
2025-06-24 11:23:55,906 INFO sqlalchemy.engine.Engine [cached since 846.4s ago] (1, datetime.date(2025, 5, 25))
INFO:sqlalchemy.engine.Engine:[cached since 846.4s ago] (1, datetime.date(2025, 5, 25))
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:55,907 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
2025-06-24 11:23:55,907 INFO sqlalchemy.engine.Engine [cached since 846.4s ago] (1, 5, 0)
INFO:sqlalchemy.engine.Engine:[cached since 846.4s ago] (1, 5, 0)
2025-06-24 11:23:55,908 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:55,908 INFO sqlalchemy.engine.Engine SELECT sum(expenses.amount) AS sum_1 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT sum(expenses.amount) AS sum_1 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER
2025-06-24 11:23:55,908 INFO sqlalchemy.engine.Engine [cached since 846.5s ago] (1, 2025, 6)
INFO:sqlalchemy.engine.Engine:[cached since 846.5s ago] (1, 2025, 6)
2025-06-24 11:23:55,909 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:55,909 INFO sqlalchemy.engine.Engine SELECT expenses.category, sum(expenses.amount) AS total_amount 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND expenses.date >= $2::DATE GROUP BY expenses.category ORDER BY total_amount DESC
INFO:sqlalchemy.engine.Engine:SELECT expenses.category, sum(expenses.amount) AS total_amount 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND expenses.date >= $2::DATE GROUP BY expenses.category ORDER BY total_amount DESC
2025-06-24 11:23:55,909 INFO sqlalchemy.engine.Engine [cached since 846.4s ago] (1, datetime.date(2025, 5, 25))
INFO:sqlalchemy.engine.Engine:[cached since 846.4s ago] (1, datetime.date(2025, 5, 25))
2025-06-24 11:23:55,910 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-06-24 11:23:55,910 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER ORDER BY expenses.amount DESC 
 LIMIT $4::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER AND EXTRACT(year FROM expenses.date) = $2::INTEGER AND EXTRACT(month FROM expenses.date) = $3::INTEGER ORDER BY expenses.amount DESC 
 LIMIT $4::INTEGER
2025-06-24 11:23:55,910 INFO sqlalchemy.engine.Engine [cached since 846.4s ago] (1, 2025, 6, 1)
INFO:sqlalchemy.engine.Engine:[cached since 846.4s ago] (1, 2025, 6, 1)
2025-06-24 11:23:55,911 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52045 - "GET /api/dashboard/stats HTTP/1.1" 200 OK
2025-06-24 11:23:55,911 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52046 - "GET /api/dashboard/summary HTTP/1.1" 200 OK
INFO:     127.0.0.1:52047 - "GET /api/expenses?limit=5&skip=0 HTTP/1.1" 200 OK
INFO:     127.0.0.1:52049 - "GET /api/dashboard/summary HTTP/1.1" 200 OK
INFO:     127.0.0.1:52048 - "GET /api/dashboard/stats HTTP/1.1" 200 OK
2025-06-24 11:23:55,917 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:55,917 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:55,917 INFO sqlalchemy.engine.Engine [cached since 846.9s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 846.9s ago] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:55,920 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
2025-06-24 11:23:55,920 INFO sqlalchemy.engine.Engine [cached since 846.4s ago] (1, 5, 0)
INFO:sqlalchemy.engine.Engine:[cached since 846.4s ago] (1, 5, 0)
2025-06-24 11:23:55,933 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52050 - "GET /api/expenses?limit=5&skip=0 HTTP/1.1" 200 OK
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:23:56,670 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:23:56,671 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:23:56,671 INFO sqlalchemy.engine.Engine [cached since 847.6s ago] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[cached since 847.6s ago] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:23:56,673 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
2025-06-24 11:23:56,674 INFO sqlalchemy.engine.Engine [cached since 847.2s ago] (1, 100, 0)
INFO:sqlalchemy.engine.Engine:[cached since 847.2s ago] (1, 100, 0)
2025-06-24 11:23:56,677 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52054 - "GET /api/expenses HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'src/email_processing/tasks.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [27391]
INFO:     Started server process [30766]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/email_processing/gmail_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [30766]
INFO:     Started server process [30774]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:25:03,538 INFO sqlalchemy.engine.Engine select pg_catalog.version()
INFO:sqlalchemy.engine.Engine:select pg_catalog.version()
2025-06-24 11:25:03,538 INFO sqlalchemy.engine.Engine [raw sql] ()
INFO:sqlalchemy.engine.Engine:[raw sql] ()
2025-06-24 11:25:03,540 INFO sqlalchemy.engine.Engine select current_schema()
INFO:sqlalchemy.engine.Engine:select current_schema()
2025-06-24 11:25:03,540 INFO sqlalchemy.engine.Engine [raw sql] ()
INFO:sqlalchemy.engine.Engine:[raw sql] ()
2025-06-24 11:25:03,541 INFO sqlalchemy.engine.Engine show standard_conforming_strings
INFO:sqlalchemy.engine.Engine:show standard_conforming_strings
2025-06-24 11:25:03,542 INFO sqlalchemy.engine.Engine [raw sql] ()
INFO:sqlalchemy.engine.Engine:[raw sql] ()
2025-06-24 11:25:03,542 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:25:03,555 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:25:03,555 INFO sqlalchemy.engine.Engine [generated in 0.00026s] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[generated in 0.00026s] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:25:03,560 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
2025-06-24 11:25:03,560 INFO sqlalchemy.engine.Engine [generated in 0.00018s] (1, 100, 0)
INFO:sqlalchemy.engine.Engine:[generated in 0.00018s] (1, 100, 0)
2025-06-24 11:25:03,577 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52185 - "GET /api/expenses HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'src/email_processing/gmail_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [30774]
INFO:     Started server process [30786]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: {'sub': '<EMAIL>', 'user_id': 1, 'exp': 1750745301}
--- get_current_user: Email from payload: <EMAIL>
--- get_current_user: Looking up user by email: <EMAIL>
2025-06-24 11:25:42,747 INFO sqlalchemy.engine.Engine select pg_catalog.version()
INFO:sqlalchemy.engine.Engine:select pg_catalog.version()
2025-06-24 11:25:42,748 INFO sqlalchemy.engine.Engine [raw sql] ()
INFO:sqlalchemy.engine.Engine:[raw sql] ()
2025-06-24 11:25:42,750 INFO sqlalchemy.engine.Engine select current_schema()
INFO:sqlalchemy.engine.Engine:select current_schema()
2025-06-24 11:25:42,750 INFO sqlalchemy.engine.Engine [raw sql] ()
INFO:sqlalchemy.engine.Engine:[raw sql] ()
2025-06-24 11:25:42,751 INFO sqlalchemy.engine.Engine show standard_conforming_strings
INFO:sqlalchemy.engine.Engine:show standard_conforming_strings
2025-06-24 11:25:42,751 INFO sqlalchemy.engine.Engine [raw sql] ()
INFO:sqlalchemy.engine.Engine:[raw sql] ()
2025-06-24 11:25:42,752 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-24 11:25:42,769 INFO sqlalchemy.engine.Engine SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.name, users.email, users.hashed_password, users.profile_image_url, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR
2025-06-24 11:25:42,769 INFO sqlalchemy.engine.Engine [generated in 0.00022s] ('<EMAIL>',)
INFO:sqlalchemy.engine.Engine:[generated in 0.00022s] ('<EMAIL>',)
--- get_current_user: User found in DB: Yes (ID: 1)
--- get_current_user: Returning user object (ID: 1)
2025-06-24 11:25:42,774 INFO sqlalchemy.engine.Engine SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT expenses.id, expenses.user_id, expenses.merchant_name, expenses.date, expenses.amount, expenses.currency, expenses.category, expenses.is_ocr_entry, expenses.ocr_raw_text, expenses.email_message_id, expenses.transaction_approval_id, expenses.extraction_confidence, expenses.created_at, expenses.updated_at 
FROM expenses 
WHERE expenses.user_id = $1::INTEGER ORDER BY expenses.date DESC, expenses.created_at DESC 
 LIMIT $2::INTEGER OFFSET $3::INTEGER
2025-06-24 11:25:42,774 INFO sqlalchemy.engine.Engine [generated in 0.00016s] (1, 100, 0)
INFO:sqlalchemy.engine.Engine:[generated in 0.00016s] (1, 100, 0)
2025-06-24 11:25:42,788 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:52228 - "GET /api/expenses HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'src/email_processing/router.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [30786]
INFO:     Started server process [30805]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/email_processing/router.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [30805]
INFO:     Started server process [30819]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/email_processing/router.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [30819]
INFO:     Started server process [30832]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/email_processing/router.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [30832]
INFO:     Started server process [30848]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     127.0.0.1:52518 - "GET /docs HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'src/email_processing/processing_rules.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [30848]
WARNING:  WatchFiles detected changes in 'src/email_processing/processing_rules.py'. Reloading...
INFO:     Started server process [31879]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Started server process [31895]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/email_processing/tasks.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [31895]
INFO:     Started server process [31944]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/email_processing/tasks.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [31944]
INFO:     Started server process [31970]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/email_processing/tasks.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [31970]
INFO:     Started server process [31981]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/email_processing/tasks.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [31981]
INFO:     Started server process [31997]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/email_processing/router.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [31997]
INFO:     Started server process [32008]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/email_processing/router.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [32008]
Process SpawnProcess-16:
Traceback (most recent call last):
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/Users/<USER>/Desktop/OCR/KharchaNepal/backend/main.py", line 31, in <module>
    from src.email_processing.router import router as email_processing_router
  File "/Users/<USER>/Desktop/OCR/KharchaNepal/backend/src/email_processing/router.py", line 405, in <module>
    async def _create_expense_from_approval(approval: TransactionApproval, db: AsyncSession) -> Optional[int]:
NameError: name 'TransactionApproval' is not defined. Did you mean: 'list_transaction_approvals'?
WARNING:  WatchFiles detected changes in 'src/email_processing/router.py'. Reloading...
Process SpawnProcess-17:
Traceback (most recent call last):
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/Users/<USER>/Desktop/OCR/KharchaNepal/backend/main.py", line 31, in <module>
    from src.email_processing.router import router as email_processing_router
  File "/Users/<USER>/Desktop/OCR/KharchaNepal/backend/src/email_processing/router.py", line 405, in <module>
    async def _create_expense_from_approval(approval: TransactionApproval, db: AsyncSession) -> Optional[int]:
NameError: name 'Optional' is not defined
WARNING:  WatchFiles detected changes in 'src/email_processing/router.py'. Reloading...
Process SpawnProcess-18:
Traceback (most recent call last):
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/Users/<USER>/Desktop/OCR/KharchaNepal/backend/main.py", line 31, in <module>
    from src.email_processing.router import router as email_processing_router
  File "/Users/<USER>/Desktop/OCR/KharchaNepal/backend/src/email_processing/router.py", line 405, in <module>
    async def _create_expense_from_approval(approval: TransactionApproval, db: AsyncSession) -> Optional[int]:
NameError: name 'Optional' is not defined
WARNING:  WatchFiles detected changes in 'src/email_processing/router.py'. Reloading...
INFO:     Started server process [36974]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: None
--- get_current_user: Payload is None, raising 401
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: None
--- get_current_user: Payload is None, raising 401
--- get_current_user: Received token: eyJhbGciOi...MsBdJzgpi4
--- get_current_user: Decoded payload: None
--- get_current_user: Payload is None, raising 401
INFO:     127.0.0.1:53477 - "GET /api/expenses HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:53624 - "GET /api/expenses HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:53671 - "GET /api/expenses HTTP/1.1" 401 Unauthorized
WARNING:  WatchFiles detected changes in 'src/email_processing/router.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [36974]
WARNING:  WatchFiles detected changes in 'src/email_processing/router.py'. Reloading...
INFO:     Started server process [37626]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Started server process [37645]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/email_processing/router.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [37645]
INFO:     Started server process [37664]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/email_processing/router.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [37664]
INFO:     Started server process [37718]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/email_processing/gmail_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [37718]
INFO:     Started server process [37821]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/email_processing/gmail_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [37821]
INFO:     Started server process [37911]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/email_processing/gmail_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [37911]
INFO:     Started server process [37935]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/email_processing/router.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [37935]
INFO:     Started server process [37952]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/email_processing/router.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [37952]
INFO:     Started server process [37968]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'database.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [37968]
Process SpawnProcess-29:
Traceback (most recent call last):
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/Users/<USER>/Desktop/OCR/KharchaNepal/backend/main.py", line 20, in <module>
    from src.auth.router import router as auth_router
  File "/Users/<USER>/Desktop/OCR/KharchaNepal/backend/src/auth/router.py", line 7, in <module>
    from database import get_db
  File "/Users/<USER>/Desktop/OCR/KharchaNepal/backend/database.py", line 24, in <module>
    sync_engine = create_engine(
  File "<string>", line 2, in create_engine
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/sqlalchemy/util/deprecations.py", line 281, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/sqlalchemy/engine/create.py", line 602, in create_engine
    dbapi = dbapi_meth(**dbapi_args)
  File "/opt/anaconda3/envs/kharchanepal/lib/python3.10/site-packages/sqlalchemy/dialects/postgresql/psycopg2.py", line 696, in import_dbapi
    import psycopg2
ModuleNotFoundError: No module named 'psycopg2'
