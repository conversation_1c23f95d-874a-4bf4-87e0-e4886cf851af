{
  "name": "vite_react_shadcn_ts",
  "private": true,
  "version": "0.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "build:dev": "vite build --mode development",
    "lint": "eslint .",
    "preview": "vite preview"
  },
  "dependencies": {
    "@hookform/resolvers": "^3.10.0",
    "@radix-ui/react-accordion": "^1.2.0",
    "@radix-ui/react-alert-dialog": "^1.1.1",
    "@radix-ui/react-aspect-ratio": "^1.1.0",
    "@radix-ui/react-avatar": "^1.1.0",
    "@radix-ui/react-checkbox": "^1.1.1",
    "@radix-ui/react-collapsible": "^1.1.0",
    "@radix-ui/react-context-menu": "^2.2.1",
    "@radix-ui/react-dialog": "^1.1.2",
    "@radix-ui/react-dropdown-menu": "^2.1.1",
    "@radix-ui/react-hover-card": "^1.1.1",
    "@radix-ui/react-icons": "^1.3.2",
    "@radix-ui/react-label": "^2.1.0",
    "@radix-ui/react-menubar": "^1.1.1",
    "@radix-ui/react-navigation-menu": "^1.2.0",
    "@radix-ui/react-popover": "^1.1.1",
    "@radix-ui/react-progress": "^1.1.0",
    "@radix-ui/react-radio-group": "^1.2.0",
    "@radix-ui/react-scroll-area": "^1.1.0",
    "@radix-ui/react-select": "^2.1.1",
    "@radix-ui/react-separator": "^1.1.0",
    "@radix-ui/react-slider": "^1.2.0",
    "@radix-ui/react-slot": "^1.1.0",
    "@radix-ui/react-switch": "^1.1.0",
    "@radix-ui/react-tabs": "^1.1.0",
    "@radix-ui/react-toast": "^1.2.1",
    "@radix-ui/react-toggle": "^1.1.0",
    "@radix-ui/react-toggle-group": "^1.1.0",
    "@radix-ui/react-tooltip": "^1.1.4",
<<<<<<< HEAD
    "@tanstack/react-query": "^5.56.2",
    "@types/qs": "^6.9.18",
    "axios": "^1.8.4",
=======
    "@swc/core": "^1.12.6",
    "@tanstack/react-query": "^5.56.2",
    "@types/qs": "^6.9.18",
    "axios": "^1.7.7",
>>>>>>> 25888b3e9796b69d13a9bce85ebf21c10d4f0bfa
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "cmdk": "^1.0.0",
    "date-fns": "^3.6.0",
<<<<<<< HEAD
    "embla-carousel-react": "^8.3.0",
    "framer-motion": "^12.9.4",
=======
    "dlv": "^1.1.3",
    "embla-carousel-react": "^8.3.0",
>>>>>>> 25888b3e9796b69d13a9bce85ebf21c10d4f0bfa
    "immer": "^10.1.1",
    "input-otp": "^1.2.4",
    "jwt-decode": "^4.0.0",
    "lucide-react": "^0.462.0",
    "next-themes": "^0.3.0",
    "qs": "^6.14.0",
    "react": "^18.3.1",
    "react-day-picker": "^8.10.1",
    "react-dom": "^18.3.1",
    "react-dropzone": "^14.3.8",
<<<<<<< HEAD
    "react-hook-form": "^7.56.0",
=======
    "react-hook-form": "^7.58.1",
>>>>>>> 25888b3e9796b69d13a9bce85ebf21c10d4f0bfa
    "react-resizable-panels": "^2.1.3",
    "react-router-dom": "^6.30.0",
    "recharts": "^2.12.7",
    "sonner": "^1.5.0",
    "tailwind-merge": "^2.5.2",
    "tailwindcss-animate": "^1.0.7",
    "vaul": "^0.9.3",
<<<<<<< HEAD
    "zod": "^3.24.3"
=======
    "zod": "^3.25.67"
>>>>>>> 25888b3e9796b69d13a9bce85ebf21c10d4f0bfa
  },
  "devDependencies": {
    "@eslint/js": "^9.9.0",
    "@tailwindcss/typography": "^0.5.15",
    "@types/node": "^22.14.1",
    "@types/react": "^18.3.20",
    "@types/react-dom": "^18.3.0",
    "@vitejs/plugin-react-swc": "^3.5.0",
<<<<<<< HEAD
    "autoprefixer": "^10.4.20",
=======
    "autoprefixer": "^10.4.21",
>>>>>>> 25888b3e9796b69d13a9bce85ebf21c10d4f0bfa
    "esbuild": "^0.25.3",
    "eslint": "^9.9.0",
    "eslint-plugin-react-hooks": "^5.1.0-rc.0",
    "eslint-plugin-react-refresh": "^0.4.9",
    "globals": "^15.9.0",
<<<<<<< HEAD
    "postcss": "^8.4.47",
=======
    "postcss": "^8.5.6",
>>>>>>> 25888b3e9796b69d13a9bce85ebf21c10d4f0bfa
    "tailwindcss": "^3.4.11",
    "typescript": "^5.5.3",
    "typescript-eslint": "^8.0.1",
    "vite": "^6.3.3"
  }
}
