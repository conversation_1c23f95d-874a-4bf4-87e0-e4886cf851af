name: kharchanepal
channels:
  - defaults
  - conda-forge
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
dependencies:
  - annotated-types=0.7.0=pyhd8ed1ab_1
  - aom=3.9.1=h7bae524_0
  - brotli-python=1.1.0=py310hb4ad77e_2
  - bzip2=1.0.8=h80987f9_6
  - c-ares=1.34.5=h5505292_0
  - ca-certificates=2025.4.26=hbd8a1cb_0
  - cairo=1.18.4=h6a3b0d2_0
  - catalogue=2.0.10=py310hbe9552e_1
  - certifi=2025.1.31=pyhd8ed1ab_0
  - cffi=1.17.1=py310h497396d_0
  - charset-normalizer=3.4.1=pyhd8ed1ab_0
  - click=8.1.8=pyh707e725_0
  - cloudpathlib=0.21.0=pyhd8ed1ab_1
  - cloudpickle=3.1.1=pyhd8ed1ab_0
  - colorama=0.4.6=pyhd8ed1ab_1
  - confection=0.1.5=pyhecae5ae_0
  - cymem=2.0.11=py310h853098b_0
  - cython-blis=1.0.1=py310h003b70b_0
  - dav1d=1.2.1=hb547adb_0
  - dbus=1.13.6=h3818c69_3
  - expat=2.7.0=h286801f_0
  - ffmpeg=7.1.1=gpl_h583251a_104
  - font-ttf-dejavu-sans-mono=2.37=hab24e00_0
  - font-ttf-inconsolata=3.000=h77eed37_0
  - font-ttf-source-code-pro=2.038=h77eed37_0
  - font-ttf-ubuntu=0.83=h77eed37_3
  - fontconfig=2.15.0=h1383a14_1
  - fonts-conda-ecosystem=1=0
  - fonts-conda-forge=1=0
  - freetype=2.13.3=hce30654_1
  - fribidi=1.0.10=h27ca646_0
  - gdk-pixbuf=2.42.12=h7ddc832_0
  - gmp=6.3.0=h7bae524_2
  - graphite2=1.3.13=hebf3989_1003
  - h2=4.2.0=pyhd8ed1ab_0
  - harfbuzz=11.1.0=hab40de2_0
  - hdf5=1.14.6=nompi_ha698983_101
  - hpack=4.1.0=pyhd8ed1ab_0
  - hyperframe=6.1.0=pyhd8ed1ab_0
  - icu=75.1=hfee45f7_0
  - idna=3.10=pyhd8ed1ab_1
  - imath=3.1.12=h025cafa_0
  - jasper=4.2.5=h743e416_0
  - jinja2=3.1.6=pyhd8ed1ab_0
  - krb5=1.21.3=h237132a_0
  - lame=3.100=h1a8c8d9_1003
  - langcodes=3.3.0=pyhd8ed1ab_0
  - lcms2=2.17=h7eeda09_0
  - lerc=4.0.0=hd64df32_1
  - libabseil=20250127.1=cxx17_h07bc746_0
  - libaec=1.1.3=hebf3989_0
  - libasprintf=0.23.1=h493aca8_0
  - libass=0.17.3=h68e5b86_2
  - libavif16=1.2.1=h3861c80_2
  - libblas=3.9.0=31_h10e41b3_openblas
  - libcblas=3.9.0=31_hb3479ef_openblas
  - libcurl=8.13.0=h73640d1_0
  - libcxx=20.1.4=ha82da77_0
  - libdeflate=1.23=h5773f1b_0
  - libedit=3.1.20191231=hc8eb9b7_2
  - libev=4.33=h93a5062_2
  - libexpat=2.7.0=h286801f_0
  - libffi=3.4.4=hca03da5_1
  - libfreetype=2.13.3=hce30654_1
  - libfreetype6=2.13.3=h1d14073_1
  - libgettextpo=0.23.1=h493aca8_0
  - libgfortran=5.0.0=14_2_0_h6c33f7e_103
  - libgfortran5=14.2.0=h6c33f7e_103
  - libglib=2.84.0=hdff4504_0
  - libhwloc=2.11.2=default_hbce5d74_1001
  - libiconv=1.18=hfe07756_1
  - libintl=0.23.1=h493aca8_0
  - libjpeg-turbo=3.1.0=h5505292_0
  - liblapack=3.9.0=31_hc9a63f6_openblas
  - liblapacke=3.9.0=31_hbb7bcf8_openblas
  - liblzma=5.8.1=h39f12f2_0
  - libnghttp2=1.64.0=h6d7220d_0
  - libogg=1.3.5=h48c0fde_1
  - libopenblas=0.3.29=openmp_hf332438_0
  - libopencv=4.11.0=headless_py310h2ebfaa0_7
  - libopenvino=2025.0.0=h3f17238_3
  - libopenvino-arm-cpu-plugin=2025.0.0=h3f17238_3
  - libopenvino-auto-batch-plugin=2025.0.0=h7f72211_3
  - libopenvino-auto-plugin=2025.0.0=h7f72211_3
  - libopenvino-hetero-plugin=2025.0.0=h718ad69_3
  - libopenvino-ir-frontend=2025.0.0=h718ad69_3
  - libopenvino-onnx-frontend=2025.0.0=h1ae5b81_3
  - libopenvino-paddle-frontend=2025.0.0=h1ae5b81_3
  - libopenvino-pytorch-frontend=2025.0.0=h286801f_3
  - libopenvino-tensorflow-frontend=2025.0.0=heb6e3e1_3
  - libopenvino-tensorflow-lite-frontend=2025.0.0=h286801f_3
  - libopus=1.5.2=h48c0fde_0
  - libpng=1.6.47=h3783ad8_0
  - libprotobuf=5.29.3=hccd9074_1
  - librsvg=2.58.4=h266df6f_3
  - libsqlite=3.45.2=h091b4b1_0
  - libssh2=1.11.1=h1590b86_0
  - libtiff=4.7.0=h551f018_4
  - libusb=1.0.28=h5505292_0
  - libvorbis=1.3.7=h9f76cd9_0
  - libvpx=1.14.1=h7bae524_0
  - libwebp-base=1.5.0=h2471fea_0
  - libxcb=1.17.0=hdb1d25a_0
  - libxml2=2.13.7=h52572c6_1
  - libzlib=1.3.1=h8359307_2
  - llvm-openmp=20.1.3=hdb05f8b_0
  - markdown-it-py=3.0.0=pyhd8ed1ab_1
  - markupsafe=3.0.2=py310hc74094e_1
  - mdurl=0.1.2=pyhd8ed1ab_1
  - murmurhash=1.0.10=py310hdde5576_2
  - ncurses=6.4=h313beb8_0
  - numpy=2.2.5=py310h4d83441_0
  - opencv=4.11.0=headless_py310hc23b458_7
  - openexr=3.3.3=h10b4c9a_0
  - openh264=2.6.0=hb5b2745_0
  - openjpeg=2.5.3=h8a3d83b_0
  - openssl=3.5.0=h81ee809_0
  - packaging=25.0=pyh29332c3_1
  - pango=1.56.3=h5fd7515_1
  - pcre2=10.44=ha881caa_2
  - pillow=11.1.0=py310h61efb56_0
  - pip=25.0=py310hca03da5_0
  - pixman=0.44.2=h2f9eb0b_0
  - preshed=3.0.9=py310hdde5576_2
  - pthread-stubs=0.4=hd74edd7_1002
  - pugixml=1.15=hd3d436d_0
  - py-opencv=4.11.0=headless_py310hd066805_7
  - pycparser=2.22=pyh29332c3_1
  - pydantic=2.11.3=pyh3cfb1c2_0
  - pydantic-core=2.33.1=py310h31b3829_0
  - pygments=2.19.1=pyhd8ed1ab_0
  - pysocks=1.7.1=pyha55dd90_7
  - pytesseract=0.3.13=pyhd8ed1ab_1
  - python=3.10.13=h2469fbe_1_cpython
  - python_abi=3.10=7_cp310
  - rav1e=0.6.6=h69fbcac_2
  - readline=8.2=h1a28f6b_0
  - requests=2.32.3=pyhd8ed1ab_1
  - rich=14.0.0=pyh29332c3_0
  - sdl2=2.32.54=ha1acc90_0
  - sdl3=3.2.10=hf196eef_1
  - setuptools=75.8.0=py310hca03da5_0
  - shellingham=1.5.4=pyhd8ed1ab_1
  - smart-open=7.1.0=hd8ed1ab_0
  - smart_open=7.1.0=pyhd8ed1ab_0
  - snappy=1.2.1=h98b9ce2_1
  - spacy=3.8.5=py310hf1156d2_0
  - spacy-legacy=3.0.12=pyhd8ed1ab_0
  - spacy-loggers=1.0.5=pyhd8ed1ab_0
  - sqlite=3.45.2=hf2abe2d_0
  - srsly=2.5.1=py310h853098b_1
  - svt-av1=3.0.2=h8ab69cd_0
  - tbb=2022.1.0=h9541205_0
  - thinc=8.3.2=py310hf1156d2_1
  - tk=8.6.13=h5083fa2_1
  - tqdm=4.67.1=pyhd8ed1ab_1
  - typer=0.15.3=pyhf21524f_0
  - typer-slim=0.15.3=pyh29332c3_0
  - typer-slim-standard=0.15.3=h1a15894_0
  - typing-extensions=4.13.2=h0e9735f_0
  - typing-inspection=0.4.0=pyhd8ed1ab_0
  - typing_extensions=4.13.2=pyh29332c3_0
  - tzdata=2025a=h04d1e81_0
  - ujson=5.10.0=py310hb4ad77e_1
  - urllib3=2.4.0=pyhd8ed1ab_0
  - wasabi=1.1.3=pyhd8ed1ab_1
  - weasel=0.4.1=pyhd8ed1ab_2
  - wheel=0.45.1=py310hca03da5_0
  - wrapt=1.17.2=py310h078409c_0
  - x264=1!164.3095=h57fd34a_2
  - x265=3.5=hbc6ce65_3
  - xorg-libxau=1.0.12=h5505292_0
  - xorg-libxdmcp=1.1.5=hd74edd7_0
  - xz=5.6.4=h80987f9_1
  - zlib=1.3.1=h8359307_2
  - zstandard=0.23.0=py310h078409c_2
  - zstd=1.5.7=h6491c7d_2
  - pip:
      - dateparser==1.2.1
      - en-core-web-sm==3.8.0
      - python-dateutil==2.9.0.post0
      - pytz==2025.2
      - regex==2024.11.6
      - six==1.17.0
      - tzlocal==5.3.1
prefix: /opt/anaconda3/envs/kharchanepal
