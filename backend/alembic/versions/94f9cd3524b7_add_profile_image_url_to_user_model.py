"""Add profile_image_url to User model

Revision ID: 94f9cd3524b7
Revises: e469339692f7
Create Date: 2025-04-22 18:27:29.988319

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '94f9cd3524b7'
down_revision: Union[str, None] = 'e469339692f7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('profile_image_url', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'profile_image_url')
    # ### end Alembic commands ###
