"""add_email_processing_models

Revision ID: b47ce93dbad9
Revises: 94f9cd3524b7
Create Date: 2025-06-23 12:17:32.429982

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b47ce93dbad9'
down_revision: Union[str, None] = '94f9cd3524b7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('email_accounts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('email_address', sa.String(length=255), nullable=False),
    sa.Column('oauth_credentials', sa.Text(), nullable=True),
    sa.Column('is_active', sa.<PERSON>(), nullable=True),
    sa.Column('last_sync_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_email_accounts_email_address'), 'email_accounts', ['email_address'], unique=False)
    op.create_index(op.f('ix_email_accounts_id'), 'email_accounts', ['id'], unique=False)
    op.create_table('email_messages',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('email_account_id', sa.Integer(), nullable=False),
    sa.Column('message_id', sa.String(length=255), nullable=False),
    sa.Column('subject', sa.String(length=500), nullable=True),
    sa.Column('sender', sa.String(length=255), nullable=True),
    sa.Column('received_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('processed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('processing_status', sa.Enum('PENDING', 'PROCESSED', 'FAILED', name='processingstatusenum'), nullable=True),
    sa.Column('has_attachments', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['email_account_id'], ['email_accounts.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_email_messages_id'), 'email_messages', ['id'], unique=False)
    op.create_index(op.f('ix_email_messages_message_id'), 'email_messages', ['message_id'], unique=False)
    op.create_table('transaction_approvals',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('email_message_id', sa.Integer(), nullable=True),
    sa.Column('extracted_data', sa.JSON(), nullable=True),
    sa.Column('approval_status', sa.Enum('PENDING', 'APPROVED', 'REJECTED', name='approvalstatusenum'), nullable=True),
    sa.Column('confidence_score', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['email_message_id'], ['email_messages.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_transaction_approvals_id'), 'transaction_approvals', ['id'], unique=False)
    op.add_column('expenses', sa.Column('email_message_id', sa.Integer(), nullable=True))
    op.add_column('expenses', sa.Column('transaction_approval_id', sa.Integer(), nullable=True))
    op.add_column('expenses', sa.Column('extraction_confidence', sa.Numeric(precision=3, scale=2), nullable=True))
    op.create_foreign_key(None, 'expenses', 'transaction_approvals', ['transaction_approval_id'], ['id'])
    op.create_foreign_key(None, 'expenses', 'email_messages', ['email_message_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'expenses', type_='foreignkey')
    op.drop_constraint(None, 'expenses', type_='foreignkey')
    op.drop_column('expenses', 'extraction_confidence')
    op.drop_column('expenses', 'transaction_approval_id')
    op.drop_column('expenses', 'email_message_id')
    op.drop_index(op.f('ix_transaction_approvals_id'), table_name='transaction_approvals')
    op.drop_table('transaction_approvals')
    op.drop_index(op.f('ix_email_messages_message_id'), table_name='email_messages')
    op.drop_index(op.f('ix_email_messages_id'), table_name='email_messages')
    op.drop_table('email_messages')
    op.drop_index(op.f('ix_email_accounts_id'), table_name='email_accounts')
    op.drop_index(op.f('ix_email_accounts_email_address'), table_name='email_accounts')
    op.drop_table('email_accounts')
    # ### end Alembic commands ###
