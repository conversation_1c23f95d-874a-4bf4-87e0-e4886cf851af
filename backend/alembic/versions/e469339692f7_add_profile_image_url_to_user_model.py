"""Add profile_image_url to User model

Revision ID: e469339692f7
Revises: ee0f4b30afd7
Create Date: 2025-04-22 18:09:52.337027

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e469339692f7'
down_revision: Union[str, None] = 'ee0f4b30afd7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
