# Automated Expense Tracking Feature Implementation Plan

## 1. Current Codebase Analysis

### Existing Architecture
Your Kharcha Nepal OCR project has a solid foundation that's perfect for building automated expense tracking:

**Backend (FastAPI + SQLAlchemy)**
- **OCR Pipeline**: Tesseract + OpenCV preprocessing + spaCy NER for extracting merchant, amount, and date
- **Database Models**: User and Expense models with OCR support (`is_ocr_entry`, `ocr_raw_text` fields)
- **Authentication**: JWT-based with bcrypt password hashing
- **API Structure**: RESTful endpoints with async database operations
- **File Processing**: Existing `/api/expenses/ocr` endpoint handles image uploads and processing

**Key Strengths for Integration**
- Modular OCR service in `src/ocr/service.py` can be reused for email attachments
- Expense model already supports OCR data storage
- Async architecture ready for background processing
- Robust image preprocessing pipeline for receipt images

### Integration Points
The automated expense tracking will leverage:
1. **OCR Service**: Reuse existing `process_image_with_ocr()` and `parse_ocr_text()` functions
2. **Expense Model**: Extend with email-specific fields (email_id, approval_status, etc.)
3. **Authentication**: Use existing JWT system for Gmail OAuth integration
4. **API Endpoints**: Add new routes for email management and approval workflows

## 2. Recommended Technical Approach

### Gmail API Integration (Recommended)
**Why Gmail API over email scraping:**
- **Security**: OAuth2 authentication with granular permissions
- **Reliability**: Official Google API with rate limiting and error handling
- **Compliance**: Follows Google's security best practices
- **Scalability**: Built-in pagination and filtering capabilities

### Architecture Overview
```
User Gmail Account → Gmail API → Celery Queue → OCR Processing → Approval Queue → Database
```

**Core Components:**
1. **Gmail Service**: OAuth2 authentication and email fetching
2. **Email Parser**: Extract attachments and embedded images using python-emails
3. **Celery Workers**: Background processing for email scanning and OCR
4. **Approval Queue**: User interface for reviewing extracted transactions
5. **Database Extensions**: New models for email tracking and approval workflow

### Queue Management with Celery
**Benefits:**
- **Asynchronous Processing**: Non-blocking email scanning
- **Scalability**: Multiple workers can process emails concurrently
- **Reliability**: Task retry mechanisms and error handling
- **Monitoring**: Built-in task monitoring and logging

## 3. Sequential Implementation Plan

### Phase 1: Foundation Setup (Week 1)
**Task 1.1: Database Schema Extensions**
- Add new models: `EmailAccount`, `EmailMessage`, `TransactionApproval`
- Extend `Expense` model with email-related fields
- Create Alembic migrations

**Task 1.2: Celery Integration**
- Install and configure Celery with Redis/RabbitMQ
- Set up basic task structure and worker configuration
- Add Celery to existing FastAPI application

**Task 1.3: Gmail API Setup**
- Install Google API Python Client
- Configure OAuth2 credentials and scopes
- Create Gmail service authentication flow

### Phase 2: Email Processing Pipeline (Week 2)
**Task 2.1: Gmail Integration Service**
- Implement Gmail OAuth2 authentication
- Create email fetching and filtering logic
- Add email account management endpoints

**Task 2.2: Email Content Extraction**
- Integrate python-emails for MIME parsing
- Extract attachments and embedded images
- Implement bank/e-wallet email detection

**Task 2.3: OCR Integration for Emails**
- Adapt existing OCR service for email attachments
- Process embedded receipt images
- Extract transaction data from email content

### Phase 3: Approval Workflow (Week 3)
**Task 3.1: Transaction Queue System**
- Create approval queue data models
- Implement queue management logic
- Add transaction review endpoints

**Task 3.2: Frontend Integration**
- Create approval queue UI components
- Add email account connection interface
- Implement transaction review and approval flows

**Task 3.3: Automated Processing Rules**
- Add confidence scoring for auto-approval
- Implement merchant recognition patterns
- Create user-configurable processing rules

### Phase 4: Security and Optimization (Week 4)
**Task 4.1: Security Hardening**
- Implement credential encryption at rest
- Add audit logging for email access
- Set up rate limiting and monitoring

**Task 4.2: Error Handling and Monitoring**
- Add comprehensive error handling
- Implement email processing status tracking
- Create monitoring dashboards

**Task 4.3: Testing and Documentation**
- Write comprehensive unit and integration tests
- Create user documentation
- Performance optimization and tuning

## 4. Alternative Solutions

### Option 1: IMAP Integration
**Pros**: Direct email access, works with any email provider
**Cons**: Less secure, requires email passwords, limited filtering capabilities
**Recommendation**: Not recommended due to security concerns

### Option 2: Email Forwarding Rules
**Pros**: Simple setup, works with existing email infrastructure
**Cons**: Manual setup required, less automated, potential for missed emails
**Recommendation**: Consider as a fallback option

### Option 3: Bank API Integration
**Pros**: Direct access to transaction data, highly accurate
**Cons**: Limited bank support, complex integration, regulatory requirements
**Recommendation**: Future enhancement after email-based system is stable

## 5. Security and Privacy Considerations

### Data Protection
- **Encryption at Rest**: Encrypt stored email credentials and content
- **Minimal Data Storage**: Store only necessary transaction data, not full email content
- **Data Retention**: Implement automatic cleanup of processed emails
- **Access Controls**: Role-based access to email processing features

### OAuth2 Security
- **Scope Limitation**: Request minimal Gmail permissions (readonly access)
- **Token Management**: Secure storage and automatic refresh of access tokens
- **Credential Rotation**: Regular rotation of OAuth2 credentials
- **Audit Logging**: Track all email access and processing activities

### Compliance Considerations
- **GDPR Compliance**: User consent and data deletion capabilities
- **Financial Data Handling**: Follow PCI DSS guidelines where applicable
- **User Privacy**: Clear disclosure of data processing activities

## 6. Required Dependencies and Integrations

### New Python Packages
```bash
# Gmail API and authentication
google-api-python-client==2.134.0
google-auth==2.30.0
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.0

# Email parsing and processing
emails==0.6.0
python-dateutil==2.8.2

# Queue management
celery==5.3.4
redis==5.0.1  # or kombu for RabbitMQ

# Security and encryption
cryptography==41.0.7
```

### Infrastructure Requirements
- **Message Broker**: Redis (recommended) or RabbitMQ for Celery
- **Background Workers**: Celery worker processes
- **Monitoring**: Flower for Celery monitoring (optional)

### Configuration Updates
- **Environment Variables**: Gmail OAuth2 credentials, Celery broker URL
- **Database**: New tables for email accounts and approval queue
- **API Endpoints**: New routes for email management and approval workflow

## 7. Success Metrics and Monitoring

### Key Performance Indicators
- **Processing Accuracy**: OCR extraction accuracy for email attachments
- **Processing Speed**: Time from email receipt to approval queue
- **User Adoption**: Number of connected email accounts and processed transactions
- **Error Rates**: Failed email processing and OCR extraction rates

### Monitoring Setup
- **Email Processing Status**: Track successful/failed email processing
- **OCR Performance**: Monitor extraction accuracy and processing time
- **Queue Health**: Monitor Celery task queue status and worker health
- **Security Events**: Track authentication failures and suspicious activities

## Next Steps

1. **Review and Approve Plan**: Confirm technical approach and timeline
2. **Set Up Development Environment**: Install dependencies and configure services
3. **Begin Phase 1 Implementation**: Start with database schema and Celery setup
4. **Iterative Development**: Implement features incrementally with testing
5. **User Testing**: Gather feedback during approval workflow development

This implementation plan provides a secure, scalable foundation for automated expense tracking while leveraging your existing OCR infrastructure and maintaining high security standards for financial data processing.
